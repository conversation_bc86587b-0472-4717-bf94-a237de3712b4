import {AxiosHeaders, AxiosResponse} from 'axios';
import {POST} from '../../../lib/http-methods';
import extractJWTokens from '../utils/jwt/extract-tokens';
import {setItemAsync} from 'expo-secure-store';

export async function refreshToken(onSuccess: () => any) {
  const {refresh} = await extractJWTokens();
  const headers = {} as AxiosHeaders;

  try {
    const res: AxiosResponse = await POST(`/tokens/refresh`, headers, {
      token: refresh,
    });

    // Use React Native secure storage instead of localStorage
    await setItemAsync('access', res.data.access);

    return onSuccess();
  } catch (error) {
    return null;
  }
}
