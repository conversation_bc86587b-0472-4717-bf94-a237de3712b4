import {AxiosError, AxiosHeaders, AxiosResponse} from 'axios';
import {POST} from '../../../lib/http-methods';
import {UserSignInType} from '../types';
import {setItemAsync} from 'expo-secure-store';

type AuthResponse = {
  status: number;
  ok: boolean;
  errors?: string;
};

export async function signIn(data: UserSignInType): Promise<AuthResponse> {
  const headers = {} as AxiosHeaders;

  try {
    const res: AxiosResponse = await POST(`/auths/login`, headers, data);
    const tokens = res.data as {access: string; refresh: string};
    await setItemAsync('access', tokens.access);
    await setItemAsync('refresh', tokens.refresh);

    return {status: 204, ok: true};
  } catch (error) {
    console.warn;
    const axiosError = error as AxiosError;

    const responseStatus = axiosError?.response?.status
      ? axiosError?.response?.status
      : 500;

    return {
      status: responseStatus,
      errors: '',
      ok: false,
    };
  }
}
