// Simple validation functions for React Native without zod dependency

import {AuthFormData, AuthValidationResult, UserSignInType} from '../types';

export interface ValidationResult {
  isValid: boolean;
  message: string;
}

export function validateEmail(email: string): ValidationResult {
  if (!email || email.trim() === '') {
    return {
      isValid: false,
      message: 'Email is required',
    };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      message: 'Invalid email format',
    };
  }

  return {
    isValid: true,
    message: '',
  };
}

export function validatePassword(password: string): ValidationResult {
  if (!password || password.trim() === '') {
    return {
      isValid: false,
      message: 'Password is required',
    };
  }

  if (password.length < 8) {
    return {
      isValid: false,
      message: 'Password must be at least 8 characters',
    };
  }

  return {
    isValid: true,
    message: '',
  };
}

export function validateName(name: string): ValidationResult {
  if (!name || name.trim() === '') {
    return {
      isValid: false,
      message: 'Name is required',
    };
  }

  if (name.length < 2) {
    return {
      isValid: false,
      message: 'Name must be at least 2 characters',
    };
  }

  return {
    isValid: true,
    message: '',
  };
}

export function validatePasswordConfirmation(
  password: string,
  confirmPassword: string,
): ValidationResult {
  if (!confirmPassword || confirmPassword.trim() === '') {
    return {
      isValid: false,
      message: 'Password confirmation is required',
    };
  }

  if (password !== confirmPassword) {
    return {
      isValid: false,
      message: 'Passwords do not match',
    };
  }

  return {
    isValid: true,
    message: '',
  };
}

export function validateCode(code: string): ValidationResult {
  if (!code || code.trim() === '') {
    return {
      isValid: false,
      message: 'Code is required',
    };
  }

  if (code.length < 5) {
    return {
      isValid: false,
      message: 'Code must be at least 5 characters',
    };
  }

  return {
    isValid: true,
    message: '',
  };
}

export function validateSignInForm(data: UserSignInType): AuthValidationResult {
  const emailValidation = validateEmail(data.email);
  const passwordValidation = validatePassword(data.password);

  const warnings = {
    email: emailValidation.message,
    password: passwordValidation.message,
    firstName: '',
    lastName: '',
    confirmPassword: '',
    generalWarning: '',
  };

  const isValid = emailValidation.isValid && passwordValidation.isValid;

  if (!isValid) {
    warnings.generalWarning = 'Please fix the errors above';
  }

  return {
    isValid,
    warnings,
  };
}

export function validateSignUpForm(data: AuthFormData): AuthValidationResult {
  const emailValidation = validateEmail(data.email);
  const passwordValidation = validatePassword(data.password);
  const firstNameValidation = validateName(data.firstName || '');
  const lastNameValidation = validateName(data.lastName || '');
  const confirmPasswordValidation = validatePasswordConfirmation(
    data.password,
    data.confirmPassword || '',
  );

  const warnings = {
    email: emailValidation.message,
    password: passwordValidation.message,
    firstName: firstNameValidation.message,
    lastName: lastNameValidation.message,
    confirmPassword: confirmPasswordValidation.message,
    generalWarning: '',
  };

  const isValid =
    emailValidation.isValid &&
    passwordValidation.isValid &&
    firstNameValidation.isValid &&
    lastNameValidation.isValid &&
    confirmPasswordValidation.isValid;

  if (!isValid) {
    warnings.generalWarning = 'Please fix the errors above';
  }

  return {
    isValid,
    warnings,
  };
}
