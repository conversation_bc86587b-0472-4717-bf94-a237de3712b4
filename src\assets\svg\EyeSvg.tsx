import * as React from 'react';
import Svg, {G, Path, Defs, ClipPath} from 'react-native-svg';

const EyeSvg: React.FC = (): JSX.Element => (
  <Svg width={16} height={16} fill='none'>
    <G
      clipPath='url(#a)'
      stroke='#142535'
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <Path d='M.667 8S3.333 2.667 8 2.667 15.333 8 15.333 8s-2.666 5.333-7.333 5.333S.667 8 .667 8Z' />
      <Path d='M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z' />
    </G>
    <Defs>
      <ClipPath id='a'>
        <Path fill='#fff' d='M0 0h16v16H0z' />
      </ClipPath>
    </Defs>
  </Svg>
);

export default EyeSvg;
