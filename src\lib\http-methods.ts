import axios, {RawAxiosRequestHeaders} from 'axios';
import {BASE_URL} from '../config/config';

const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000000, //10 seconds
});

apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    console.error('Axios request error:', error);
    return Promise.reject(error);
  },
);

apiClient.interceptors.response.use(
  (response) => {
    console.warn('Axios response:', {
      url: response.config.url,
      status: response.status,
      data: response.data,
    });
    return response;
  },
  (error) => {
    console.error('Axios response error:', {
      url: error.config?.url,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });
    return Promise.reject(error);
  },
);

export function GET(
  uri: string,
  headers: RawAxiosRequestHeaders,
  params?: any,
) {
  return apiClient.get(uri, {headers: headers, ...params});
}

export async function POST(
  uri: string,
  headers: RawAxiosRequestHeaders,
  data: any,
  params?: any,
) {
  try {
    const res = await apiClient.post(uri, data, {headers, ...params});
    console.warn('POST response:', {
      status: res.status,
      data: res.data,
      headers: res.headers,
    });
    return res;
  } catch (error) {
    console.error('POST error:', error);
    throw error;
  }
}

export function PUT(
  uri: string,
  headers: RawAxiosRequestHeaders,
  data: any,
  params?: any,
) {
  return apiClient.put(uri, data, {headers, ...params});
}

export function PATCH(
  uri: string,
  headers: RawAxiosRequestHeaders,
  data: any,
  params?: any,
) {
  return apiClient.patch(uri, data, {headers, ...params});
}

export function DELETE(
  uri: string,
  headers: RawAxiosRequestHeaders,
  params?: any,
) {
  return apiClient.delete(uri, {headers: headers, ...params});
}
