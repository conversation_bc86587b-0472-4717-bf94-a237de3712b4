{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/webpack-config": "^19.0.0", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@reduxjs/toolkit": "^1.9.5", "@tanstack/react-query": "^5.84.1", "axios": "^1.11.0", "expo": "~49.0.10", "expo-font": "~11.4.0", "expo-linear-gradient": "~12.3.0", "expo-secure-store": "~12.3.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.4", "react-native-collapsible": "^1.6.1", "react-native-dashed-line": "^1.1.0", "react-native-flash-message": "^0.4.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^13.0.1", "react-native-parsed-text": "^0.0.22", "react-native-phone-input": "^1.3.7", "react-native-responsive-dimensions": "^3.1.1", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-web": "~0.19.6", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@types/react": "~18.2.14", "typescript": "^5.1.3"}, "private": true}